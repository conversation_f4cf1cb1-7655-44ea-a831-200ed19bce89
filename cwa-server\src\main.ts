import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import * as cors from 'cors';
import { GridFSService } from './gridfs/gridfs.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule,{bodyParser:true});

  // Global pipes
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );
app.use(cors(
  {
    origin:'*',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  }
));

  // Add direct image serving route (before global prefix)
  const gridfsService = app.get(GridFSService);

  app.use('/:filename(.*\\.(jpg|jpeg|png|gif|webp|svg|ico))', async (req, res, next) => {
    try {
      const filename = req.params.filename;

      // Validate filename to prevent directory traversal attacks
      if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        return next();
      }

      const file = await gridfsService.findFileByFilename(filename);

      if (!file) {
        return next();
      }

      // Set security headers
      res.set({
        'Content-Type': file.metadata?.mimetype || 'image/jpeg',
        'Cache-Control': 'public, max-age=3600',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY'
      });

      // Create a read stream and pipe it to the response
      const readStream = gridfsService.createReadStreamByFilename(filename);

      readStream.on('error', (error) => {
        console.error('GridFS read stream error:', error);
        if (!res.headersSent) {
          return next();
        }
      });

      readStream.pipe(res);
    } catch (error) {
      console.error('Image serving error:', error);
      next();
    }
  });


  // Global prefix
  app.setGlobalPrefix('api');



  await app.listen(3000);
  console.log(`Application is running on: ${await app.getUrl()}`);
  console.log(`Swagger documentation available at: ${await app.getUrl()}/api/docs`);
}
bootstrap();
